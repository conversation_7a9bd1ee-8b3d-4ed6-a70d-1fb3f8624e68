#!/bin/sh

##############################################################################
# Steps and Details of this script
# 
# 1. Select repository which you want to release
# 2. Fetch latest updates from the selected repository
#     1. Fetch latest codes
#     2. Checkout master branch
#
# 3. Add tag to your local
#     1. Show recent release tag list
#     2. Enter tag name
#     3. Enter tag message
#     4. Add tag
# 
# 4. Push tag to remote
#     1. Make sure if push
#     2. Push tag to remote
# 
# 5. Pipeline status
#     1. Call GitLab API to receive pipeline info
#     2. Show web_url
#       - Requires `jq` command
##############################################################################
set -e

BASE_DIR=${HOME}/src/gitlab.com/innopm
DENEB_DIR=${BASE_DIR}/deneb
VEGA_DIR=${BASE_DIR}/vega
DOCS_DIR=${BASE_DIR}/docs

GITLAB_DENEB_ID=14346166
GITLAB_VEGA_ID=15087786
GITLAB_DOCS_ID=15067292

declare WORKING_DIR
declare RELEASE_TAG
declare GITLAB_PROJECT_ID
declare RELEASE_TARGET

function cancel() {
    echo "Operation canceled by user"
    exit 2
}

echo "---------------------------------------------------------------"
echo "Start releasing by ${0} at "`date +"%Y-%m-%d %H:%M:%S"`
echo "---------------------------------------------------------------\n"

##################################################
# 1. Select repository which you want to release
##################################################
echo "Select repository:"
REPO_LIST=("deneb" "vega" "docs" "exit")

PS3="Please put a number from the repository list: "
select ITEM in ${REPO_LIST[@]}
do
    echo "Repository: ${ITEM}\n"
    RELEASE_TARGET=${ITEM}

    case ${REPLY} in
        "1" )
            WORKING_DIR=${DENEB_DIR}
            GITLAB_PROJECT_ID=${GITLAB_DENEB_ID}
            break
            ;;
        "2" )
            WORKING_DIR=${VEGA_DIR}
            GITLAB_PROJECT_ID=${GITLAB_VEGA_ID}
            break
            ;;
        "3" )
            WORKING_DIR=${DOCS_DIR}
            GITLAB_PROJECT_ID=${GITLAB_DOCS_ID}
            break
            ;;
        "4" )
            cancel
            ;;
        * )
            echo "Please put number from the list above"
            ;;
    esac
done

### change directory to the target repo
echo "Changing directory"
pushd ${WORKING_DIR} > /dev/null

#######################################################
# 2. Fetch latest updates from the selected repository
#######################################################
echo "Fetching latest master"
git fetch -p && git checkout -B master origin/master
echo ""

##################################################
# 3. Add tag to your local
##################################################
### fetch remote tags
echo "Adding tag\n"
declare TAG_REGEX
# show recent tags
echo "Tags in a past few times:"
PAST_TAGS=`git ls-remote -t --refs --sort=-v:refname | sed "s/refs\/tags\///" | sed "s/^/    /"`
if [ "${RELEASE_TARGET}" = "deneb" ]; then
    TAG_REGEX="^(deneb|zeus)-v[0-9]{1,2}\.[0-9]{1,2}\.[0-9]{1,2}$"
    echo "  deneb:"
    echo "${PAST_TAGS}" | grep deneb | head -n5
    echo "  zeus:"
    echo "${PAST_TAGS}" | grep zeus | head -n5
elif [ "${RELEASE_TARGET}" = "vega" ]; then
    TAG_REGEX="^release_.*$"
    echo "  vega:"
    echo "${PAST_TAGS}" | grep -E 'release_.*$' | head -n5
elif [ "${RELEASE_TARGET}" = "docs" ]; then
    TAG_REGEX="^v[0-9]{1,2}\.[0-9]{1,2}\.[0-9]{1,2}$"
    echo "${PAST_TAGS}" | head -n5
else
    echo "error happened: unknown release target"
    exit 2
fi

while [[ ! "${RELEASE_TAG}" =~ ${TAG_REGEX} ]]
do
    read -p "Enter tag name: " RELEASE_TAG
    echo "tag name: ${RELEASE_TAG}\n"
    if [[ ! "${RELEASE_TAG}" =~ ${TAG_REGEX} ]]; then
        echo "NG: Enter tag name with right format"
    fi
done

while [ ${#MESSAGE} -eq 0 ]
do
    read -p "Enter tag message: " MESSAGE
    echo "message: ${MESSAGE}\n"
    if [ ${#MESSAGE} -gt 0 ]; then
        # add tag
        git tag -a ${RELEASE_TAG} -m "${MESSAGE}"
        # show tag info
        echo "Tag Info:"
        git tag -n1 -l "${RELEASE_TAG}"
    fi
done

##################################################
# 4. Push tag to remote
##################################################
read -p "Are you sure to push tag to remote? [y/n]: " RESULT
if [ "$RESULT" = "y" ]; then
    echo "OK"
    git push origin ${RELEASE_TAG}

    ### done pushing tag
    echo "---------------------------------------------------------------"
    echo "Done pushing new tag at "`date +"%Y-%m-%d %H:%M:%S"`
    echo "---------------------------------------------------------------\n"
else
    cancel
fi

### back to the previous directory
echo "Back to previous directory\n"
popd > /dev/null

##################################################
# 5. Pipeline status
##################################################
## Check pipeline status
sleep 5
## ${GITLAB_PRIVATE_TOKEN} is from env var
## e.g.) export GITLAB_PRIVATE_TOKEN='xxxxx'
echo "Check pipeline status from the link below: "
curl -s -H "PRIVATE-TOKEN: ${GITLAB_PRIVATE_TOKEN}" "https://gitlab.com/api/v4/projects/${GITLAB_PROJECT_ID}/pipelines?ref=${RELEASE_TAG}" | jq ".[].web_url" | sed 's/"//g'

echo ""
echo "-----------------------------------------------------------------------------------"
echo "Completed releasing ${RELEASE_TARGET}(${RELEASE_TAG}) by ${0} at "`date +"%Y-%m-%d %H:%M:%S"`
echo "-----------------------------------------------------------------------------------\n"
