
# Checklist for assignees
- [ ] Confirmed behavior on your local environment
- [ ] Passed all unit test
- [ ] Optimized commit and commit message
- [ ] Updated issue
    - URL of specs
    - Screen shots
    - etc.


# Note for reviewer
コメントする際は、以下のラベルを頭につけてもらえるとコメントの温度感が伝わります。  
Put the following label on your comment to let assignees know importance.

| Label  | Description                                                                                                                                                     |
| ------ | --------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [MUST] | 問題があり、必ず直すべきものに対して。 <br>There's a problem that needs to be fixed.                                                                            |
| [IMO]  | 意見、緩やかな指摘。自分ならこう書くけどどうでしょう？<br>Tell your opinion and share your advice. I'll write codes like this, how do you think...?, and so on. |
| [nits] | ほんの小さな指摘。インデントミスなどの細かいところに。<br> Tiny indication like miss indenting and miss spelling, etc.                                          |
| [ASK]  | わからないことに対しての質問。<br>Asking questions about things you don't understand.                                                                           |
| [IMH]  | 独り言。コードに対する感想。 <br>Monologue. Your thoughts about codes.                                                                                          |

# Review guideline
[Frontend review guideline](https://www.notion.so/crowdlogjp/Code-Review-Guide-9c0315f646a3443da23cd6222d7e7a07)
