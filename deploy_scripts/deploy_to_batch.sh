#!/bin/bash

# This is a script to push TARGET_SCRIPTS into TARGET_SV by SCP.
# Before executing, please ensure that the host PC meets the following conditions.
# [conditions]
#   - To install 'scp' library.
#   - To connect Ebisu-VPN or CrowdLog-VPN.
#   - To finish ssh config to connect Batch Server.
#   - To set USER_NAME at ".env".

set -euo pipefail

source ./.env

# Select environment
if [ ! $# -ge 1 ]; then
  echo "Faild. an arg for environment required."
  echo "  --stg: for staging"
  echo "  --prd: for production"
  exit 1
fi

if [ $1 == "--prd" ]; then
  # Need to set USER_NAME at dotEnv.
  SCP_USER_NAME=$USER_NAME
  SCP_HOST="innopm-b01-v2"
  TARGET_PATH=/home/<USER>/
  TARGET_SCRIPTS=("db_migration.sh" "release.sh")

  echo "Environment => Prodction"
else
  SCP_USER_NAME=ec2-user
  SCP_HOST=innopm-stg-b01
  TARGET_PATH=/opt/ec2-user/scripts/
  TARGET_SCRIPTS=("db_migration.sh" "release.sh")

  echo "Environment => Staging"
fi

for sc in ${TARGET_SCRIPTS[@]}; do
  scp ./$sc $SCP_USER_NAME@$SCP_HOST:$TARGET_PATH$sc
  echo "Done to push '$sc'"
  echo "------------"
done

echo ""
echo "Finish to push all scripts."
echo "But Deploy has not done yet. You need to do processes below."
echo "  1. Move the scripts from ec2-user directory('$TARGET_PATH') to inpm directory('/opt/inpm/script')."
echo "  2. Change the permission of scripts."
