# inpm-scripts
This repository is stored self-created scripts such as used in releasing operation.

# Purpose
Purpose of this repository is to be easier maintenance for self-created scripts and reduce incidents by human error.  

For example, `release.sh` is always used in release operation but it's not managed by git.  
There are some risks such as lost script, use old version and so on.

# Scripts
## db_migration.sh
It can be easier to migrate DDL to each envionment.  
If you want to use as a command, rename filename to 'db_migration'

```
% cp ./db_migration.sh /usr/local/bin/db_migration
% chmod +x /usr/local/bin/db_migration
```

### Usage

```
./db_migration.sh -e [local|stg|prd] -f [SQL file]
```

## run-stg-server.sh
This is shell to start `inpm-stg-w01` server.  
The access key has privillage more than running server.  
Please not CHANGE AWS CLI COMMAND without asking.  

In addition, you need to ask AWS administrator about AWS related values.  
Then, you must set those values into environment variables in advance.  

### Set Environment Variables
e.g.) `~/.bashrc`, `~/.zshrc`, etc.  
```sh
### AWS ENV
export AWS_ACCESS_KEY_ID=XXXXXXXXXXXXX
export AWS_SECRET_ACCESS_KEY=XXXXXXXXX
export AWS_DEFAULT_REGION=XXXXXXXXXXXX
export INSTANCE_ID=XXXXXXXXXXXXXXXXXXX
```


```sh
$ source ~/.bashrc
```

### Usage

```
./run-stg-server.sh
```

#### Check status and Start server

```
% ./run-stg-server.sh
check server status ===============
[
    [
        {
            "Status": "running",
            "Name": [
                "inpm-web02",
                "monitored"
            ]
        }
    ]
]
do you want to run the server?[y]
```

- Type `n` or `Ctrl + C` to kill the script
- Type `y` to start stg server

## release.sh
This is a script to release source codes to server.  

### Usage
```
./release.sh
```

Then, follow the message will be displayed.

### Set Environment Variables
e.g.) `~/.bashrc`, `~/.zshrc`, etc.  
```sh
### RL_ENV
export RL_ENV=PRD
```


```sh
$ source ~/.bashrc
```

- Use the value below
    - Production environment: `PRD`
    - Staging environment: `STG`