#!/bin/sh
# description=====
# this is shell to start inpm-stg-w01 server.
# the access key has privillage more than running server.
# Please not CHANGE AWS CLI COMMAND without asking
#
# In addition, you need to ask AWS administrator about AWS related values.
# Then, you must set those values into environment variables in advance.

# if not exists
if [ -z $(docker images -q garland/aws-cli-docker) ]; then
  echo "you need to install docker images 'docker pull garland/aws-cli-docker'"
fi

# check status
echo "check server status ==============="
docker run --rm \
--env AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID} \
--env AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY} \
--env AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION}	 \
garland/aws-cli-docker \
aws ec2 describe-instances --instance-ids ${INSTANCE_ID} \
--query 'Reservations[*].Instances[*].{Status:State.Name, Name:Tags[*].Value}'

echo 'do you want to run the server?[y]'
read ANS

if [ ${ANS} = "y" ]; then
  # run server
  docker run --rm \
  --env AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID} \
  --env AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY} \
  --env AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION}	 \
  garland/aws-cli-docker \
  aws ec2 start-instances --instance-ids ${INSTANCE_ID}
fi


