#!/bin/sh

declare DENEB_COMMIT_HASHE
declare RELEASE_PATH=/mnt/efs/inpm/app/innopm/
declare MOD_TAGS_FILE_PATH=private/lib/Config/ModTags.yaml

# check user
if [ "${USER}" != "inpm" ]; then
    echo "ERROR:It's not inpm user!"
    exit 9
fi

echo ""
echo "---------------------------------------------------------------"
echo " Start updating ModTag.yaml"
echo "---------------------------------------------------------------"
echo ""

echo ">>> Fetch commit hash of master from deneb ..."
DENEB_COMMIT_HASHE=$(git ls-remote --heads --refs https://gitlab.com/innopm/deneb.git master | awk '{print $1}' | cut -c 1-8)

if [ -n "${DENEB_COMMIT_HASHE}" ]; then

    # RELEASE_PATH
    pushd ${RELEASE_PATH} > /dev/null
    echo ">>> current directory is "`pwd`
    if [ ! -e ${MOD_TAGS_FILE_PATH} ]; then
        echo "---------------------------------------------------------------"
        echo " Config/ModTags.yaml not exists for ${RELEASE_PATH}"
        echo "---------------------------------------------------------------"
        echo ""
        popd > /dev/null
        exit 2;
    fi
    echo ""
    echo "---------------------------------------------------------------"
    echo " deneb master commit hash => ${DENEB_COMMIT_HASHE}"
    echo "---------------------------------------------------------------"
    echo ""
    sed -i -e "s/remote:.*/remote: ${DENEB_COMMIT_HASHE}/" ${MOD_TAGS_FILE_PATH}

    cat private/lib/Config/ModTags.yaml

    echo ""
    echo "---------------------------------------------------------------"
    echo " End update ModTag.yaml for ${RELEASE_PATH}"
    echo "---------------------------------------------------------------"
    echo ""
    popd > /dev/null
    echo "################################################################"
    echo "#"
    echo "# ModTag.yaml update all finished at "`date +"%Y-%m-%d %H:%M:%S"`
    echo "#"
    echo "################################################################"
else
    echo ""
    echo "---------------------------------------------------------------"
    echo " ERROR: Could not get commit hash for deneb"
    echo " Git authentication may have failed"
    echo " No update ModTag.yaml"
    echo "---------------------------------------------------------------"
    echo ""
    exit 2;
fi
