#!/usr/bin/env bash

if (( BASH_VERSINFO[0] < 4 )); then
    echo "Error: This script requires Bash 4.0 or higher to use coproc. Current version: ${BASH_VERSION}" >&2
    exit 1
fi

##############################################################################
## Exit by Ctrl+C signal
##############################################################################
trap 'echo -e "\n\nForce Terminated"; exit 1' INT

##############################################################################
##
## This script is for deleting data of churned companies on each environment.
##
##############################################################################

usage() {
    echo "invalid parameter"
    echo -n "    Usage: "
    echo "${0} -e [local|stg|mask|prd] -d [DELETION SQL directory]"

    exit 1
}

select_schema() {
    local schemas=("$@")
    local array_length=${#schemas[@]}

    if [ $array_length -eq 0 ]; then
        echo "Err: Empty Schema!" >&2
        return 1
    fi

    while true; do
        echo "Select Schema:" >&2
        for ((i=0; i<$array_length; i++)); do
            echo "$((i+1)). ${schemas[$i]}" >&2
        done

        read -p "Please Input Number (1~$array_length): " input
        if [ $? -ne 0 ]; then
            echo -e "\nForce Terminated." >&2
            exit 1
        fi
        echo >&2

        if [[ "$input" =~ ^[0-9]+$ ]] && 
           [ "$input" -ge 1 ] && 
           [ "$input" -le $array_length ]; then
            local index=$((input-1))
            echo "${schemas[$index]}"
            return 0
        else
            echo "Warning: Invalid Input!" >&2
            echo >&2
        fi
    done
}

receive_company_ids() {
    while true; do
        read -p "Please Input <company_no> (ex:「6099」 Separate by comma if multiple, ex:「6099,6103,7288」): " input
        if [ $? -ne 0 ]; then
            echo -e "\nForce Terminated." >&2
            exit 1
        fi
        echo >&2

        if [[ "$input" =~ ^[0-9]+(,[0-9]+)*$ ]]; then
            IFS=, read -ra comp_array <<< "$input"
            comp_array=(${comp_array[@]})

            if [ ${#comp_array[@]} -eq 0 ]; then
                echo "Warning: Invalid Input!" >&2
                continue
            fi

            printf "%s\n" "${comp_array[@]}"
            return 0
        else
            echo "Warning: Invalid Input!" >&2
            echo >&2
        fi
    done
}

input_start_table_number() {
    # list the tables with number
    echo "##############################################" >&2
    printf "  %-15s %-30s\n" "TABLE NUMBER" "TABLE NAME" >&2
    echo "----------------------------------------------" >&2
    ls -p "${QUERY_DIRECTORY}" | grep -v '/$' | awk -F'.sql' '{print $1}' | while read -r line; do
        printf "  %-15s %-30s\n" "${line%%_*}" "${line#*_}" >&2
    done
    echo "##############################################" >&2
    echo >&2

    while true; do
        read -p "Please Input the Table Number where deletion work starts from: " input
        if [ $? -ne 0 ]; then
            echo -e "\nForce Terminated." >&2
            exit 1
        fi
        echo >&2

        if [[ "$input" =~ ^[0-9]+$ ]] && 
           [ "$input" -ge 10000 ] && 
           [ "$input" -le 39999 ]; then
            start_from_sql_file=$(find "${QUERY_DIRECTORY}" -maxdepth 1 -type f -name "${input}_*.sql" 2>/dev/null | head -n 1)
            if [ -z "$start_from_sql_file" ]; then
                echo "Warning: Number ${input} does not match a table! Please reconfirm the table list." >&2
                echo >&2
                continue
            fi
            echo "$input"
            return 0
        else
            echo "Warning: Invalid Input!" >&2
            echo >&2
        fi
    done
}

if [ ${#} -ne 4 ]; then
    usage
fi

while getopts d:e: OPT
do
    case ${OPT} in
        d) QUERY_DIRECTORY="${OPTARG%/}"
           ;;
        e) ENVIRONMENT=${OPTARG}
           ;;
        *) usage;;
    esac
done

if [ ! -d ${QUERY_DIRECTORY} ]; then
    echo "Err! no such directory exists: ${QUERY_DIRECTORY}" >&2
    exit 1
fi

read -sp "DB Password for [${ENVIRONMENT}]: " password
echo

# local docker env
if [ ${ENVIRONMENT} = "local" ]; then
    DB_LIST=("innopm" "innopm_01" "innopm_common")
    db=$(select_schema "${DB_LIST[@]}")
    mysql_conn="docker exec -i inpm-db mysql ${db} -u root -p${password}"
    deletion_log_dirname="company_db_deletion_logs"
    if [[ "${db}" == "innopm_common" ]]; then
        deletion_log_dirname="common_db_deletion_logs"
    fi
    echo "You entered: ${db}"

# stg env
elif [ ${ENVIRONMENT} = "stg" ]; then
    DB_LIST=("dev_inpm" "dev_inpm05" "dev_inpm06" "dev_inpm_com")
    db=$(select_schema "${DB_LIST[@]}")
    mysql_conn="mysql ${db} -u dev_inpm_user -h inpm-db.cxn81hgctojw.ap-northeast-1.rds.amazonaws.com -p${password}"
    deletion_log_dirname="company_db_deletion_logs"
    if [[ "${db}" == "dev_inpm_com" ]]; then
        deletion_log_dirname="common_db_deletion_logs"
    fi
    echo "You entered: ${db}"

# mask env
elif [ ${ENVIRONMENT} = "mask" ]; then
    HOST_LIST=("mask-inpm-db-05" "mask-inpm-db-common")
    host=$(select_schema "${HOST_LIST[@]}")
    mysql_conn="mysql prd_inpm -u prd_inpm_user -h ${host}.cxn81hgctojw.ap-northeast-1.rds.amazonaws.com -p${password}"
    deletion_log_dirname="company_db_deletion_logs"
    if [[ "${host}" == "mask-inpm-db-common" ]]; then
        deletion_log_dirname="common_db_deletion_logs"
    fi
    echo "You entered: ${host}"

# prd env
elif [ ${ENVIRONMENT} = "prd" ]; then
    # for rds instances
    HOST_LIST=("innopm-db01" "innopm-db05" "innopm-common")
    host=$(select_schema "${HOST_LIST[@]}")
    mysql_conn="mysql prd_inpm -u prd_inpm_user -h ${host}.cpyruf4twabc.ap-northeast-1.rds.amazonaws.com -p${password}"
    deletion_log_dirname="company_db_deletion_logs"
    if [[ "${host}" == "innopm-common" ]]; then
        deletion_log_dirname="common_db_deletion_logs"
    fi
    echo "You entered: ${host}"

else
    usage
fi


##############################################################################
## Table deletion process
##############################################################################
C_NO_LIST=()
while IFS= read -r line; do
    C_NO_LIST+=("$line")
done < <(receive_company_ids)

for c_no in "${C_NO_LIST[@]}"
do
    echo
    logfile="deletion_log_cno_${c_no}_$(date +%Y%m%d%H%M%S).log"
    mkdir -p "./${deletion_log_dirname}/${c_no}"

    # check if company exists
    exists=$(${mysql_conn} -sN -e "SELECT COUNT(*) FROM company WHERE company_no = ${c_no}")
    if [ $exists -eq 0 ]; then
        echo "[$(date +'%Y-%m-%d %H:%M:%S')] The company does not exist! c_no: ${c_no}" | tee -a "./${deletion_log_dirname}/${c_no}/${logfile}"
        echo
        continue
    fi

    # check if company_status=0 then skip deleting
    contracting=$(${mysql_conn} -sN -e "SELECT COUNT(*) FROM company WHERE company_no = ${c_no} AND company_status = 0")
    if [ $contracting -eq 1 ]; then
        echo "[$(date +'%Y-%m-%d %H:%M:%S')] This is NOT a churned company! Skip deleting because it is still contracting. c_no: ${c_no}" | tee -a "./${deletion_log_dirname}/${c_no}/${logfile}"
        echo
        continue
    fi

    echo "[$(date +'%Y-%m-%d %H:%M:%S')] Start deletion work for c_no: $c_no" | tee -a "./${deletion_log_dirname}/${c_no}/${logfile}"
    echo

    start_table_number=$(input_start_table_number)
    start_from_sql_file=$(find "${QUERY_DIRECTORY}" -maxdepth 1 -type f -name "${start_table_number}_*.sql" 2>/dev/null | head -n 1)
    echo "Deletion work will start from sql file '${start_from_sql_file}'"
    echo

    for file_path in "${QUERY_DIRECTORY}"/*.sql; do

        # get table number and table name
        table_name=$(basename "${file_path%.sql}")
        table_number=${table_name%%_*}
        table_name=${table_name#*_}

        # skip tables that table number is less than start table number
        if [[ ! $table_number =~ ^[1-3][0-9]{4}$ ]] || [ $table_number -lt $start_table_number ]; then
            continue
        fi

        select_query=$(sed -n '/^DELETE/q;p' "${file_path}"; echo x)
        select_query="${select_query%x}"

        rows_affected=$(${mysql_conn} -sN -e "
            SET @company_no = ${c_no};
            ${select_query}
            SET @company_no = NULL;
        ")

        read -r -p "[$(date +'%Y-%m-%d %H:%M:%S')] c_no: ($c_no)  table: '${table_name}'  total_rows_to_DELETE: ${rows_affected}. OK TO DELETE? (y/n): " choice
        echo

        if ! [[ $choice =~ [Yy] ]]; then
            echo "[$(date +'%Y-%m-%d %H:%M:%S')] c_no: ($c_no)  table_number: '${table_number}'  table: '${table_name}'  total_rows_to_DELETE: ${rows_affected}. DELETION has been SKIPPED." | tee -a "./${deletion_log_dirname}/${c_no}/${logfile}"
            echo
            continue
        fi

        echo "[$(date +'%Y-%m-%d %H:%M:%S')] table: '${table_name}'. BEGIN DELETING TABLE.... (c_no: $c_no)" | tee -a "./${deletion_log_dirname}/${c_no}/${logfile}"
        echo

        # Start mysql coproc (unbuffered)
        coproc MYSQL_PROC (${mysql_conn} -vvv --unbuffered)
        if [[ -z "${MYSQL_PROC[1]}" || -z "${MYSQL_PROC[0]}" ]]; then
            echo "Error: coproc failed to start. Exiting." >&2
            exit 1
        fi

        # copy file descriptors (as 3 input, 4 output)
        exec 3>&${MYSQL_PROC[1]} 4<&${MYSQL_PROC[0]}

        # start transaction
        transaction_sql="BEGIN;SET @company_no = ${c_no};$(cat "${file_path}")SET @company_no = NULL;"
        echo "${transaction_sql}" >&3

        # read output of mysql
        rows_affected=0
        while IFS= read -r line <&4; do
            [[ "$line" == *"Using a password"* ]] && continue
            echo "$line" # | tee -a "./${deletion_log_dirname}/${c_no}/${logfile}"

            if [[ "$line" =~ Query\ OK,\ ([0-9]+)\ rows\ affected ]]; then
                rows_affected="${BASH_REMATCH[1]}"
            fi

            if [[ "$line" == *"SET @company_no = NULL"* ]]; then
                break
            fi
        done

        # commit or rollback
        echo "--------------"
        echo ">>> Please Input 'COMMIT;↵' or 'ROLLBACK;↵'"
        exec_sql_command=""
        while IFS= read -r user_input; do
            echo "$user_input" >&3
            if [[ "$user_input" =~ ^(COMMIT|ROLLBACK)\;$ ]]; then
                exec_sql_command="${BASH_REMATCH[1]}"
                break
            fi
        done

        # exit mysql coproc
        echo "exit" >&3
        wait "${MYSQL_PROC_PID}"

        # clean up file descriptors
        exec 3>&- 4<&-

        # real total deleted rows
        deleted_rows_total=${rows_affected}
        if [[ "${exec_sql_command}" == "ROLLBACK" ]]; then
            deleted_rows_total=0
        fi
        echo "[$(date +'%Y-%m-%d %H:%M:%S')] table_number: '${table_number}'. table: '${table_name}'. deleted_rows_total: ${deleted_rows_total}. DELETING TABLE has ${exec_sql_command} successfully! (c_no: ${c_no})" | tee -a "./${deletion_log_dirname}/${c_no}/${logfile}"
        echo
    done

    echo "[$(date +'%Y-%m-%d %H:%M:%S')] Deletion work finished! (c_no: $c_no)" | tee -a "./${deletion_log_dirname}/${c_no}/${logfile}"
done
