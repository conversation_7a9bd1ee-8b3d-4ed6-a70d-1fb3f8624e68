SELECT COUNT(*)
FROM am_timesheet_fix
INNER JOIN
(
		SELECT t.timesheet_no
		FROM timesheet t
		INNER JOIN employee e
		ON e.employee_no = t.employee_no
		AND e.company_no = @company_no
) tmp
ON am_timesheet_fix.timesheet_no = tmp.timesheet_no;

DELETE am_timesheet_fix
FROM am_timesheet_fix
INNER JOIN
(
		SELECT t.timesheet_no
		FROM timesheet t
		INNER JOIN employee e
		ON e.employee_no = t.employee_no
		AND e.company_no = @company_no
) tmp
ON am_timesheet_fix.timesheet_no = tmp.timesheet_no;
