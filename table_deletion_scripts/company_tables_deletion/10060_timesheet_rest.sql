SELECT COUNT(*)
FROM timesheet_rest
INNER JOIN
(
		SELECT t.timesheet_no
		FROM timesheet t
		INNER JOIN employee e
		ON e.employee_no = t.employee_no
		AND e.company_no = @company_no
) tmp
ON timesheet_rest.timesheet_no = tmp.timesheet_no;

DELETE timesheet_rest
FROM timesheet_rest
INNER JOIN
(
		SELECT t.timesheet_no
		FROM timesheet t
		INNER JOIN employee e
		ON e.employee_no = t.employee_no
		AND e.company_no = @company_no
) tmp
ON timesheet_rest.timesheet_no = tmp.timesheet_no;
