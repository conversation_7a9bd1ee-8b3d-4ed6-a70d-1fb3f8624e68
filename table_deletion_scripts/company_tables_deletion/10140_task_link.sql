SELECT COUNT(*)
FROM task_link
WHERE EXISTS (
    SELECT 1
    FROM task t
    INNER JOIN project p ON t.project_no = p.project_no
    WHERE p.company_no = @company_no
      AND (t.task_no = task_link.source_task_no OR t.task_no = task_link.target_task_no)
);

DELETE FROM task_link
WHERE EXISTS (
    SELECT 1
    FROM task t
    INNER JOIN project p ON t.project_no = p.project_no
    WHERE p.company_no = @company_no
      AND (t.task_no = task_link.source_task_no OR t.task_no = task_link.target_task_no)
);
