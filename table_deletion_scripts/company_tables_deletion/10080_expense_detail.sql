SELECT COUNT(*)
FROM expense_detail
INNER JOIN expense_data
ON expense_detail.expense_no = expense_data.expense_no
INNER JOIN employee em
ON em.employee_no = expense_data.employee_no
AND em.company_no = @company_no;

DELETE expense_detail
FROM expense_detail
INNER JOIN expense_data
ON expense_detail.expense_no = expense_data.expense_no
INNER JOIN employee em
ON em.employee_no = expense_data.employee_no
AND em.company_no = @company_no;
