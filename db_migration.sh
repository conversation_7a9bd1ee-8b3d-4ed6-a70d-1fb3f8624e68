#!/bin/bash

##################################################################
## This script can be easier to migrate DDL to each environment.
##
## If you want to use as a command, rename filename to 'db_migration'
##
## % cp ./db_migration.sh /usr/local/bin/db_migration
## % chmod +x /usr/local/bin/db_migration
##
##################################################################

usage() {
    echo "invalid parameter"
    echo -n "    Usage: "
    echo "${0} -e [local|stg|prd] -f [SQL file]"

    exit 1
}

if [ ${#} -ne 4 ]; then
    usage
fi

while getopts f:e: OPT
do
    case ${OPT} in
        f) QUERY_FILE=${OPTARG}
           ;;
        e) ENVIRONMENT=${OPTARG}
           ;;
        *) usage;;
    esac
done

if [ ${ENVIRONMENT} = "local" ]; then
    DB_LIST=("innopm innopm_01")
    read -sp "Password for ${ENVIRONMENT}: " password
    for db in ${DB_LIST}
    do
        docker exec -i inpm-db mysql ${db} -u root -p${password} < ${QUERY_FILE}
    done

elif [ ${ENVIRONMENT} = "stg" ]; then
    DB_LIST=("stg_inpm stg_inpm05 stg_inpm06")
    read -sp "Password for [stg]: " password
    for db in ${DB_LIST}
    do
        mysql ${db} -u dev_inpm_user -h inpm-db.cxn81hgctojw.ap-northeast-1.rds.amazonaws.com -p${password} < ${QUERY_FILE}
    done

elif [ ${ENVIRONMENT} = "prd" ]; then
    # for rds instances
    HOST_LIST=("innopm-db01 innopm-db02 innopm-db04 innopm-db05 innopm-db07 innopm-db08 innopm-db09")
    for host in ${HOST_LIST}
    do
        echo ${host}:
        mysql prd_inpm -u prd_inpm_user -h ${host}.cpyruf4twabc.ap-northeast-1.rds.amazonaws.com -p < ${QUERY_FILE}
    done

    # for aurora instances
    AURORA_HOST_LIST=(
        "inpm-prd-private-db001-cluster"
        "inpm-prd-public-db001-cluster"
    )
    for aurora_host in "${AURORA_HOST_LIST[@]}"
    do
      echo "${aurora_host}":
      mysql prd_inpm -u prd_inpm_user -h "${aurora_host}.cluster-cpyruf4twabc.ap-northeast-1.rds.amazonaws.com" -p < "${QUERY_FILE}"
    done
else
    usage
fi
